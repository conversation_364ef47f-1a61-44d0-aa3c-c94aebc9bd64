class TattoosController < ApplicationController
  require 'open-uri'
  require 'net/http'
  # require 'json'

  def index
    @tattoos = Tattoo.all
    render json: @tattoos
  end

  def show
    @tattoo = Tattoo.find(params[:id])
    render json: @tattoo
  end

  def create
    image_url = call_remaker_api(prompt, style, body_placement, size)
    filename = "#{SecureRandom.hex(10)}.png"
    local_image_path = download_image(image_url, filename)

    @tattoo = Tattoo.create(
      prompt: params[:prompt],
      style: params[:style],
      body_placement: params[:body_placement],
      size: params[:size],
      image_url: params[:image_url],
      local_image_url: params[:local_image_path]
    )

    if @tattoo.valid?
      render json: @tattoo
    else
      render json: { errors: @tattoo.errors.full.message }
    end

    private 

    def call_remaker_api(prompt, style, body_placement, size) 
      uri = URI("https://remaker.ai/ai-tattoo-generator/")

      headers = {
        "Authorization" => "Bearer #{ENV['REMAKER_API_KEY']}",
        "Content-Type" => "application/json"
      }
      body = {
        prompt: prompt, 
        style: style, 
        placement: body_placement,
        size: size,
    }.to_json 

    response = Net::HTTP.start(uri.host, uri.port, use_ssl: true) do |http|
      req = Net::HTTP::Post.new(uri, headers)
      req.body = body
      http.request(req)
    end
    
    data = JSON.parse(response.body)

    data["image_url"] || data["result"]["image"]
  end


  def download_image(image_url, filename)
    download_image = URI.open(image_url)

    save_path = Rails.root.join("public", "images", filename)
    File.open(save_path, 'wb') {|file| file.write(download_image.read)}
  end
end